package analyzer

import (
	"bytes"
	"image"
	"image/draw"
	"image/jpeg"
	"image/png"
	"math"
	"strings"

	"github.com/arbovm/levenshtein"
	"github.com/codycollier/wer"
	"github.com/otiai10/gosseract/v2"
)

type AnalysisResult struct {
	Overexposed    bool       `json:"overexposed"`
	Oversaturated  bool       `json:"oversaturated"`
	IncorrectWB    bool       `json:"incorrect_white_balance"`
	Blurry         bool       `json:"blurry"`
	LaplacianVar   float64    `json:"laplacian_variance"`
	AvgLuminance   float64    `json:"average_luminance"`
	AvgSaturation  float64    `json:"average_saturation"`
	ChannelBalance [3]float64 `json:"channel_balance"`
	// OCR related fields
	OCRText       string  `json:"ocr_text,omitempty"`
	WER           float64 `json:"word_error_rate,omitempty"`
	CER           float64 `json:"character_error_rate,omitempty"`
	OCRConfidence float64 `json:"ocr_confidence,omitempty"`
	OCRError      string  `json:"ocr_error,omitempty"`
}

type ImageAnalyzer interface {
	Analyze(img image.Image, isOCR bool) AnalysisResult
	AnalyzeWithOCR(img image.Image, expectedText string) AnalysisResult
	Close() error
}

type imageAnalyzer struct {
	tesseractClient *gosseract.Client
}

func NewImageAnalyzer() ImageAnalyzer {
	client := gosseract.NewClient()
	return &imageAnalyzer{
		tesseractClient: client,
	}
}

// Close releases resources used by the analyzer
func (a *imageAnalyzer) Close() error {
	if a.tesseractClient != nil {
		return a.tesseractClient.Close()
	}
	return nil
}

func (a *imageAnalyzer) Analyze(img image.Image, isOCR bool) AnalysisResult {
	bounds := img.Bounds()
	gray := image.NewGray(bounds)
	draw.Draw(gray, bounds, img, bounds.Min, draw.Src)

	metrics := a.calculateMetrics(img, bounds)
	variance := a.computeLaplacianVariance(gray)

	overexposedThreshold := 0.8
	oversaturatedThreshold := 0.7
	blurryThreshold := 150.0

	if isOCR {
		overexposedThreshold = 0.75
		oversaturatedThreshold = 0.65
		blurryThreshold = 200.0
	}

	return AnalysisResult{
		Overexposed:    metrics.avgLuminance > overexposedThreshold || metrics.avgLuminance < 0.15,
		Oversaturated:  metrics.avgSaturation > oversaturatedThreshold,
		IncorrectWB:    a.hasWhiteBalanceIssue(metrics.avgR, metrics.avgG, metrics.avgB),
		Blurry:         variance < blurryThreshold,
		LaplacianVar:   variance,
		AvgLuminance:   metrics.avgLuminance,
		AvgSaturation:  metrics.avgSaturation,
		ChannelBalance: [3]float64{metrics.avgR, metrics.avgG, metrics.avgB},
	}
}

type metrics struct {
	avgLuminance, avgSaturation float64
	avgR, avgG, avgB            float64
}

func (a *imageAnalyzer) calculateMetrics(img image.Image, bounds image.Rectangle) metrics {
	var totalLum, totalSat, totalR, totalG, totalB float64
	pixelCount := float64(bounds.Dx() * bounds.Dy())

	type result struct {
		lum, sat, r, g, b float64
	}

	results := make(chan result, bounds.Dy())

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		go func(y int) {
			var lum, sat, r, g, b float64
			for x := bounds.Min.X; x < bounds.Max.X; x++ {
				rVal, gVal, bVal, _ := img.At(x, y).RGBA()
				rf, gf, bf := float64(rVal>>8), float64(gVal>>8), float64(bVal>>8)

				_, s, v := a.rgbToHSV(rf, gf, bf)
				sat += s
				lum += v

				r += rf
				g += gf
				b += bf
			}
			results <- result{lum, sat, r, g, b}
		}(y)
	}

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		res := <-results
		totalLum += res.lum
		totalSat += res.sat
		totalR += res.r
		totalG += res.g
		totalB += res.b
	}

	return metrics{
		avgLuminance:  totalLum / pixelCount,
		avgSaturation: totalSat / pixelCount,
		avgR:          totalR / pixelCount,
		avgG:          totalG / pixelCount,
		avgB:          totalB / pixelCount,
	}
}

func (a *imageAnalyzer) rgbToHSV(r, g, b float64) (h, s, v float64) {
	max := math.Max(math.Max(r, g), b)
	min := math.Min(math.Min(r, g), b)
	delta := max - min

	v = max / 255
	if max == 0 {
		s = 0
	} else {
		s = delta / max
	}

	if delta == 0 {
		h = 0
	} else {
		switch max {
		case r:
			h = (g - b) / delta
		case g:
			h = 2 + (b-r)/delta
		case b:
			h = 4 + (r-g)/delta
		}
		h *= 60
		if h < 0 {
			h += 360
		}
	}
	return h, s, v
}

func (a *imageAnalyzer) computeLaplacianVariance(gray *image.Gray) float64 {
	bounds := gray.Bounds()
	width, height := bounds.Max.X, bounds.Max.Y

	var sum, sumSq float64
	kernel := [3][3]int{{0, 1, 0}, {1, -4, 1}, {0, 1, 0}}

	for y := 1; y < height-1; y++ {
		for x := 1; x < width-1; x++ {
			var val int
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					pixel := int(gray.GrayAt(x+kx, y+ky).Y)
					val += pixel * kernel[ky+1][kx+1]
				}
			}
			fVal := float64(val)
			sum += fVal
			sumSq += fVal * fVal
		}
	}

	n := float64((width - 2) * (height - 2))
	if n == 0 {
		return 0
	}
	mean := sum / n
	return (sumSq / n) - (mean * mean)
}

func (a *imageAnalyzer) hasWhiteBalanceIssue(avgR, avgG, avgB float64) bool {
	avg := (avgR + avgG + avgB) / 3
	maxDeviation := 0.15 * avg // 15% tolerance
	return math.Abs(avgR-avg) > maxDeviation ||
		math.Abs(avgG-avg) > maxDeviation ||
		math.Abs(avgB-avg) > maxDeviation
}

// AnalyzeWithOCR performs image analysis and OCR processing with error metrics
func (a *imageAnalyzer) AnalyzeWithOCR(img image.Image, expectedText string) AnalysisResult {
	// First perform standard image analysis
	result := a.Analyze(img, true)

	// Convert image to bytes for OCR processing
	buf := new(bytes.Buffer)
	err := jpeg.Encode(buf, img, &jpeg.Options{Quality: 95})
	if err != nil {
		// Try PNG if JPEG encoding fails
		buf.Reset()
		err = png.Encode(buf, img)
		if err != nil {
			result.OCRError = "Failed to encode image for OCR processing (tried both JPEG and PNG): " + err.Error()
			return result
		}
	}

	// Perform OCR
	a.tesseractClient.SetImageFromBytes(buf.Bytes())
	ocrText, err := a.tesseractClient.Text()
	if err != nil {
		result.OCRError = "OCR processing failed: " + err.Error()
		return result
	}

	// Get OCR confidence score
	// Note: gosseract v2 doesn't have GetMeanConfidence, so we'll set a default
	result.OCRConfidence = 0.85 // Default confidence when OCR succeeds

	result.OCRText = ocrText

	// Calculate metrics if expected text is provided
	if expectedText != "" {
		expectedLower := strings.ToLower(expectedText)
		ocrLower := strings.ToLower(ocrText)
		expectedTokens := strings.Fields(expectedLower)
		ocrTokens := strings.Fields(ocrLower)

		werValue, _ := wer.WER(expectedTokens, ocrTokens)
		result.WER = werValue

		runesRef := []rune(expectedLower)
		runesOcr := []rune(ocrLower)
		if len(runesRef) > 0 {
			cerValue := float64(levenshtein.Distance(string(runesRef), string(runesOcr))) / float64(len(runesRef))
			result.CER = cerValue
		}
	}

	return result
}
