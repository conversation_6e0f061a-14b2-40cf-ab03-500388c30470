package storage

import (
	"context"
	"crypto/tls"
	"fmt"
	"image"
	_ "image/jpeg"
	_ "image/png"
	"net/http"
	"os"
	"strconv"
	"time"
)

type ImageFetcher interface {
	FetchImage(ctx context.Context, imageURL string) (image.Image, error)
}

type HTTPImageFetcher struct {
	client *http.Client
}

func NewHTTPImageFetcher() ImageFetcher {
	// Configure TLS settings based on environment
	tlsConfig := &tls.Config{}

	// Multiple ways to skip TLS verification for flexibility
	skipTLS := false

	// Method 1: Environment variable
	if skip, err := strconv.ParseBool(os.Getenv("SKIP_TLS_VERIFY")); err == nil && skip {
		skipTLS = true
	}

	// Method 2: Alternative environment variable names
	if !skipTLS {
		if skip, err := strconv.ParseBool(os.Getenv("DISABLE_TLS_VERIFY")); err == nil && skip {
			skipTLS = true
		}
	}

	// Method 3: Check for insecure mode
	if !skipTLS {
		if skip, err := strconv.ParseBool(os.Getenv("INSECURE_TLS")); err == nil && skip {
			skipTLS = true
		}
	}

	// Method 4: For Azure Blob Storage specifically (common issue)
	if !skipTLS {
		if skip, err := strconv.ParseBool(os.Getenv("AZURE_SKIP_TLS_VERIFY")); err == nil && skip {
			skipTLS = true
		}
	}

	if skipTLS {
		tlsConfig.InsecureSkipVerify = true
		fmt.Printf("WARNING: TLS certificate verification is disabled\n")
	}

	// Create custom transport with TLS configuration
	transport := &http.Transport{
		TLSClientConfig: tlsConfig,
		// Add other transport settings for better performance and reliability
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		IdleConnTimeout:     90 * time.Second,
		// Add proxy support if needed
		Proxy: http.ProxyFromEnvironment,
	}

	return &HTTPImageFetcher{
		client: &http.Client{
			Transport: transport,
			Timeout:   30 * time.Second,
		},
	}
}

func (h *HTTPImageFetcher) FetchImage(ctx context.Context, imageURL string) (image.Image, error) {
	req, err := http.NewRequestWithContext(ctx, "GET", imageURL, nil)
	if err != nil {
		return nil, fmt.Errorf("invalid URL: %w", err)
	}

	// Set headers for proper image handling
	req.Header.Set("Accept", "image/jpeg, image/png, image/webp, */*")
	req.Header.Set("User-Agent", "Go-Image-Inspector/1.0")

	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch image from %s: %w", imageURL, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code %d when fetching image from %s", resp.StatusCode, imageURL)
	}

	img, _, err := image.Decode(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	return img, nil
}
